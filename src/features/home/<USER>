import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axiosInstance from '../../services/axiosInstance';

export const fetchUserData = createAsyncThunk(
    'home/fetchUserData',
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get('/user/userdata');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || error.message);
        }
    }
);

const initialState = {
    name: '',
    hospital: 'Hospital',
    role: 'N/A',
    loading: false,
    error: null,
};

const homeSlice = createSlice({
    name: 'home',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchUserData.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchUserData.fulfilled, (state, action) => {
                state.loading = false;
                state.name = action.payload.name === undefined ? state.name : action.payload.name;
                state.hospital = action.payload.hospital === '' ? state.hospital : action.payload.hospital;
                state.role = action.payload.role === '' ? state.role : action.payload.role;
            })
            .addCase(fetchUserData.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    },
});

export default homeSlice.reducer; 