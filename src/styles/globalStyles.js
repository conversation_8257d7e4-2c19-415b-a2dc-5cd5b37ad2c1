import { StyleSheet } from 'react-native';
import colors from '../theme/colors';

const globalStyles = StyleSheet.create({
    

    // Checkbox (shared)
    
    checkboxBox: {
        width: 20,
        height: 20,
        borderWidth: 2,
        borderColor: colors.border?.green || '#C9CDD2',
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.white,
        marginRight:10
    },
    checkboxChecked: {
        backgroundColor: colors.button.primary || '#1E88E5',
        borderColor: colors.border?.green|| '#1E88E5',
    },
    checkmark: {
        color: '#fff',
        fontSize: 14,
        lineHeight: 20,            // match checkboxBox height for vertical centering
        textAlign: 'center',
        textAlignVertical: 'center', // Android vertical centering
        includeFontPadding: false,   // Android: remove extra font padding
    },
    

    // Radio (shared)
    radioButton: {
        width: 18,
        height: 18,
        borderRadius: 9,
        borderWidth: 2,
        borderColor: colors.border?.green || '#1E88E5',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8,
        backgroundColor: colors.white,
    },
    radioButtonSelected: {
        width: 10,
        height: 10,
        borderRadius: 5,
        backgroundColor:colors.button.primary || '#1E88E5',
    },

    //activity indicator 
    loaderContainer: {
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center',
        backgroundColor: colors.white,
    },
    
});

export default globalStyles;