export const mapSection2Data = (formData, name, TNR, hospital) => {
    const now = new Date().toISOString();
    const username = name || 'system';

    // Helper function to map array of selected options to object with only "yes" values
    const mapOptionsToYesOnly = (selectedOptions, optionMapping = {}) => {
        const result = {};

        selectedOptions?.forEach(option => {
            // Use mapping if provided, otherwise use option as-is
            const mappedKey = optionMapping[option] || option;
            result[mappedKey] = 'yes';
        });

        return result;
    };

    // Map GBS screening - only include selected options
    const gbsScreeningMapping = {
        'na': 'NA'
    };
    const gbsScreening = mapOptionsToYesOnly(formData.gbs_screening || [], gbsScreeningMapping);

    // If 'other' is selected in GBS, add the other value
    if (formData.gbs_screening?.includes('other') && formData.gbs_screening_other) {
        gbsScreening.other = formData.gbs_screening_other;
    }

    // Map abnormal serology - only include selected options
    const abnormalSerologyMapping = {
        'positive_anti_hiv': 'positive_anti_HIV',
        'positive_hbsag': 'positive_HBsAg',
        'reactive_vdrl': 'positive_VDRL',
        'na': 'NA'
    };
    const abnormalSerology = mapOptionsToYesOnly(formData.abnormal_serology || [], abnormalSerologyMapping);

    if (formData.abnormal_serology?.includes('other') && formData.abnormal_serology_other) {
        abnormalSerology.other = formData.abnormal_serology_other;
    }

    // Map complications during pregnancy - only include selected options
    const pregnancyComplicationsMapping = {
        'no_anc': 'no_ANC',
        'overt_dm_gdm': 'over_DM_or_GDM',
        'chronic_ht': 'chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia',
        'maternal_thyroid': 'meternal_thyroid_disease',
        'maternal_uti': 'meternal_UTI',
        'maternal_drug_abuse': 'meternal_drug_abuse',
        'na': 'NA'
    };
    const pregnancyComplications = mapOptionsToYesOnly(formData.complications_pregnancy || [], pregnancyComplicationsMapping);

    // Add additional fields for pregnancy complications
    if (formData.complications_pregnancy?.includes('multiple_gestation')) {
        pregnancyComplications.multiple_gestation = 'yes';
        // Add number field for twin or higher order
        if (formData.multiple_gestation_type === 'twin') {
            pregnancyComplications.number = 'Twin';
        } else if (formData.multiple_gestation_type === 'higher_order' && formData.multiple_gestation_higher_order_specify) {
            pregnancyComplications.number = formData.multiple_gestation_higher_order_specify;
        }
    }

    if (formData.complications_pregnancy?.includes('maternal_drug_abuse') && formData.maternal_drug_abuse_specify) {
        pregnancyComplications.meternal_drug_abuse = formData.maternal_drug_abuse_specify;
    }

    if (formData.complications_pregnancy?.includes('other') && formData.complications_pregnancy_other) {
        pregnancyComplications.other = formData.complications_pregnancy_other;
    }

    // Add metadata if any complications are selected
    if (Object.keys(pregnancyComplications).length > 0) {
        pregnancyComplications.status = 'save';
        pregnancyComplications.last_modified_username = username;
        pregnancyComplications.last_modified_date = now;
    }

    // Map intrapartum complications - only include selected options
    const intrapartumComplicationsMapping = {
        'fetal_distress': 'Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement',
        'maternal_fever': 'meternal_fever_or_Chrioaminonitis',
        'prolonged_rupture': 'prolonged_rupture_of_membrane_more_than_18_hr',
        'msaf': 'MSAF',
        'iugr': 'IUGR',
        'oligohydramios': 'oilgohydramios',
        'polyhydramios': 'polyhydramios',
        'na': 'NA'
    };
    const intrapartumComplications = mapOptionsToYesOnly(formData.intrapartum_complications || [], intrapartumComplicationsMapping);

    // Add other intrapartum complications if any
    if (formData.intrapartum_complications?.includes('other') && formData.intrapartum_complications_other) {
        intrapartumComplications.other = formData.intrapartum_complications_other;
    }

    // Add metadata if any complications are selected
    if (Object.keys(intrapartumComplications).length > 0) {
        intrapartumComplications.status = 'save';
        intrapartumComplications.last_modified_username = username;
        intrapartumComplications.last_modified_date = now;
    }

    // Map maternal medication - only include selected options
    const maternalMedicationMapping = {
        'dexamethasone_prenatal_steroid': 'dexamamthasone_or_prenatal_steroid',
        'mgso4': 'MgSo4',
        'no_medication': 'no_complication',
        'na': 'NA'
    };
    const maternalMedication = mapOptionsToYesOnly(formData.maternal_medication || [], maternalMedicationMapping);

    // Add timing for antibiotics if selected
    if (formData.maternal_medication?.includes('antibiotics')) {
        maternalMedication.antibiotics = 'yes';
        if (formData.antibiotics_timing) {
            maternalMedication.time = formData.antibiotics_timing;
        }
    }

    // Add course for prenatal steroid if selected
    if (formData.maternal_medication?.includes('dexamethasone_prenatal_steroid') && formData.prenatal_steroid_course) {
        maternalMedication.course = formData.prenatal_steroid_course;
    }

    // Add other medications if any (filter out empty values for payload)
    if (formData.maternal_medication?.includes('other') && formData.maternal_medication_other?.length > 0) {
        const nonEmptyOthers = formData.maternal_medication_other.filter(item => item && item.trim() !== '');
        if (nonEmptyOthers.length > 0) {
            maternalMedication.other = nonEmptyOthers.join(', ');
        }
    }

    // Add metadata if any medications are selected
    if (Object.keys(maternalMedication).length > 0) {
        maternalMedication.status = 'save';
        maternalMedication.last_modified_username = username;
        maternalMedication.last_modified_date = now;
    }

    // Add metadata to GBS screening if any options are selected
    if (Object.keys(gbsScreening).length > 0) {
        gbsScreening.status = 'save';
        gbsScreening.last_modified_username = username;
        gbsScreening.last_modified_date = now;
    }

    // Add metadata to abnormal serology if any options are selected
    if (Object.keys(abnormalSerology).length > 0) {
        abnormalSerology.status = 'save';
        abnormalSerology.last_modified_username = username;
        abnormalSerology.last_modified_date = now;
    }

    // Build the main data object - only include fields that have values
    const data = {
        TNR: TNR,
        status: 'save',
        last_modified_username: username,
        last_modified_date: now
    };

    // Add maternal age and age_NA fields
    if (formData.maternal_age_type === 'na') {
        data.age_NA = 'yes';
    } else if (formData.maternal_age) {
        data.age = formData.maternal_age;
    }

    // Add GPA values and NA fields
    if (formData.gpa_type === 'na') {
        data.G_NA = 'yes';
        data.P_NA = 'yes';
        data.A_NA = 'yes';
    } else {
        if (formData.gpa_g) {
            data.G = formData.gpa_g;
        } else {
            data.G_NA = 'yes';
        }
        if (formData.gpa_p) {
            data.P = formData.gpa_p;
        } else {
            data.P_NA = 'yes';
        }
        if (formData.gpa_a) {
            data.A = formData.gpa_a;
        } else {
            data.A_NA = 'yes';
        }
    }

    // Construct the final payload - only include sections that have data
    const payload = { data };

    if (Object.keys(abnormalSerology).length > 0) {
        payload.data.abnormal_serology = abnormalSerology;
    }

    if (Object.keys(pregnancyComplications).length > 0) {
        payload.data.complication_during_pregnancy = pregnancyComplications;
    }

    if (Object.keys(intrapartumComplications).length > 0) {
        payload.data.intrapartum_complication = intrapartumComplications;
    }

    if (Object.keys(maternalMedication).length > 0) {
        payload.data.meternal_medication = maternalMedication;
    }

    if (Object.keys(gbsScreening).length > 0) {
        payload.data.gbs = gbsScreening;
    }

    return payload;
};


export const mapSection2ResponseToForm = (data, setters = {}) => {
    if (!data) return {};

    const {
        setValue,
        setOtherMedicationFields,
    } = setters;

    // --- Maternal Age & GPA ---
    if (data.age_NA === 'yes') {
        setValue('maternal_age_type', 'na', { shouldValidate: true });
    } else if (data.age) {
        setValue('maternal_age', data.age, { shouldValidate: true });
        setValue('maternal_age_type', 'age', { shouldValidate: true });
    }

    if (data.G_NA === 'yes' && data.P_NA === 'yes' && data.A_NA === 'yes') {
        setValue('gpa_type', 'na', { shouldValidate: true });
    } else {
        setValue('gpa_g', data.G || '', { shouldValidate: true });
        setValue('gpa_p', data.P || '', { shouldValidate: true });
        setValue('gpa_a', data.A || '', { shouldValidate: true });
        // Set gpa_type based on which fields have values
        if (data.G) {
            setValue('gpa_type', 'g', { shouldValidate: true });
        } else if (data.P) {
            setValue('gpa_type', 'p', { shouldValidate: true });
        } else if (data.A) {
            setValue('gpa_type', 'a', { shouldValidate: true });
        }
    }

    // --- Abnormal Serology ---
    const abnormalKeys = [
        'positive_anti_HIV', 'positive_HBsAg', 'positive_VDRL', 'normal_all', 'other', 'NA'
    ];
    const abnormalSelected = [];
    if (data.abnormal_serology) {
        abnormalKeys.forEach((key) => {
            if (data.abnormal_serology[key] && data.abnormal_serology[key] !== '') {
                abnormalSelected.push(key === 'NA' ? 'na' : key === 'positive_anti_HIV' ? 'positive_anti_hiv' :
                    key === 'positive_HBsAg' ? 'positive_hbsag' : key === 'positive_VDRL' ? 'reactive_vdrl' : key);
            }
        });
        setValue('abnormal_serology', abnormalSelected, { shouldValidate: true });

        if (data.abnormal_serology.other) {
            setValue('abnormal_serology_other', data.abnormal_serology.other, { shouldValidate: true });
        }
    }

    // --- Complications During Pregnancy ---
    const pregnancyKeys = [
        'no_ANC', 'over_DM_or_GDM', 'chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia',
        'meternal_thyroid_disease', 'multiple_gestation', 'meternal_UTI', 'meternal_drug_abuse', 'other', 'NA'
    ];
    const pregnancySelected = [];
    if (data.complication_during_pregnancy) {
        pregnancyKeys.forEach((key) => {
            if (data.complication_during_pregnancy[key] && data.complication_during_pregnancy[key] !== '') {
                pregnancySelected.push(key === 'no_ANC' ? 'no_anc' :
                    key === 'over_DM_or_GDM' ? 'overt_dm_gdm' :
                    key === 'chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia' ? 'chronic_ht' :
                    key === 'meternal_thyroid_disease' ? 'maternal_thyroid' :
                    key === 'meternal_UTI' ? 'maternal_uti' :
                    key === 'meternal_drug_abuse' ? 'maternal_drug_abuse' :
                    key === 'multiple_gestation' ? 'multiple_gestation' : key
                );
            }
        });

        setValue('complications_pregnancy', pregnancySelected, { shouldValidate: true });

        if (data.complication_during_pregnancy.multiple_gestation && data.complication_during_pregnancy.number) {
            const type = data.complication_during_pregnancy.number === 'Twin' ? 'twin' : 'higher_order';
            setValue('multiple_gestation_type', type, { shouldValidate: true });
            if (type === 'higher_order') {
                setValue('multiple_gestation_higher_order_specify', data.complication_during_pregnancy.number, { shouldValidate: true });
            }
        }

        if (data.complication_during_pregnancy.meternal_drug_abuse) {
            setValue('maternal_drug_abuse_specify', data.complication_during_pregnancy.meternal_drug_abuse, { shouldValidate: true });
        }

        if (data.complication_during_pregnancy.other) {
            setValue('complications_pregnancy_other', data.complication_during_pregnancy.other, { shouldValidate: true });
        }
    }

    // --- Intrapartum Complications ---
    const intrapartumKeys = [
        'Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement', 'meternal_fever_or_Chrioaminonitis',
        'prolonged_rupture_of_membrane_more_than_18_hr', 'MSAF', 'IUGR', 'oilgohydramios', 'polyhydramios', 'abnormal_vaginal_bleeding', 'prolapsed_cord', 'other', 'NA'
    ];
    const intrapartumSelected = [];
    if (data.intrapartum_complication) {
        intrapartumKeys.forEach((key) => {
            if (data.intrapartum_complication[key] && data.intrapartum_complication[key] !== '') {
                intrapartumSelected.push(key === 'Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement' ? 'fetal_distress' :
                    key === 'meternal_fever_or_Chrioaminonitis' ? 'maternal_fever' :
                    key === 'prolonged_rupture_of_membrane_more_than_18_hr' ? 'prolonged_rupture' :
                    key === 'MSAF' ? 'msaf' :
                    key === 'IUGR' ? 'iugr' :
                    key === 'oilgohydramios' ? 'oligohydramios' :
                    key === 'polyhydramios' ? 'polyhydramios' :
                    key === 'abnormal_vaginal_bleeding' ? 'abnormal_vaginal_bleeding' :
                    key === 'prolapsed_cord' ? 'prolapsed_cord' :
                    key === 'NA' ? 'na' : 'other'
                );
            }
        });
        setValue('intrapartum_complications', intrapartumSelected, { shouldValidate: true });

        if (data.intrapartum_complication.other) {
            setValue('intrapartum_complications_other', data.intrapartum_complication.other, { shouldValidate: true });
        }
    }

    // --- Maternal Medication ---
    const maternalKeys = [
        'dexamamthasone_or_prenatal_steroid', 'MgSo4', 'no_complication', 'antibiotics', 'other', 'NA'
    ];
    const maternalSelected = [];
    if (data.meternal_medication) {
        maternalKeys.forEach((key) => {
            if (data.meternal_medication[key] && data.meternal_medication[key] !== '') {
                maternalSelected.push(key === 'dexamamthasone_or_prenatal_steroid' ? 'dexamethasone_prenatal_steroid' :
                    key === 'no_complication' ? 'no_medication' : key);
            }
        });
        setValue('maternal_medication', maternalSelected, { shouldValidate: true });

        // Handle other medication dynamic fields
        if (data.meternal_medication.other) {
            const otherValues = data.meternal_medication.other.split(',').map(v => v.trim());
            setOtherMedicationFields(otherValues.map((val, i) => ({ id: i + 1, value: val })));
            // Also set the form values to ensure validation works
            setValue('maternal_medication_other', otherValues, { shouldValidate: true });
        } else if (maternalSelected.includes('other')) {
            // If 'other' is selected but no values exist, ensure the field is initialized
            setValue('maternal_medication_other', [], { shouldValidate: true });
        }

        if (data.meternal_medication.time) {
            setValue('antibiotics_timing', data.meternal_medication.time, { shouldValidate: true });
        }

        if (data.meternal_medication.course) {
            setValue('prenatal_steroid_course', data.meternal_medication.course, { shouldValidate: true });
        }
    }

    // --- GBS ---
    const gbsKeys = ['positive', 'negative', 'not_done', 'other', 'NA'];
    const gbsSelected = [];
    if (data.gbs) {
        gbsKeys.forEach((key) => {
            if (data.gbs[key] && data.gbs[key] !== '') {
                gbsSelected.push(key === 'NA' ? 'na' : key);
            }
        });
        setValue('gbs_screening', gbsSelected, { shouldValidate: true });

        if (data.gbs.other) {
            setValue('gbs_screening_other', data.gbs.other, { shouldValidate: true });
        }
    }

    // --- TNR & Hospital ---
    if (data.TNR) {
        setValue('TNR', data.TNR, { shouldValidate: true });
    }
    if (data.hospital) {
        setValue('hospital', data.hospital, { shouldValidate: true });
    }
};

export default mapSection2Data;
