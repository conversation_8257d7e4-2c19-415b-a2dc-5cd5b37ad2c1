import { useContext, useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, TextInput, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useDispatch, useSelector } from 'react-redux';
import globalStyles from '../../styles/globalStyles';
import colors from '../../theme/colors';
import LanguageContext from '../../context/LanguageContext';
import ScreenWrapper from '../../components/ScreenWrapper';
import CommonHeader from '../../components/CommonHeader';
import CommonFooter from '../../components/CommonFooter';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { section3DefaultValues, section3Schema } from '../../schemas/section3Schema';
import mapSection3Data from '../../helpers/sections/section3';
import { saveSectionThunk, updateSectionProgressThunk } from '../../features/sections/sectionSlice';
import PopupCard from '../../components/PopupCard';
import CompleteIcon from '../../assets/img/complete.svg';


const Section3Form = () => {
  const { t } = useContext(LanguageContext);
  const router = useRouter();
  const dispatch = useDispatch();
  const { loading } = useSelector((state) => state.sections);
  const { TNR, name } = useLocalSearchParams();

  const resolver = useMemo(() => zodResolver(section3Schema), []);
  const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: section3DefaultValues,
    resolver,
    mode: 'onChange',
  });

  const radioSelect = (field, value) => setValue(field, value, { shouldValidate: true });

  const clearField = (field) => setValue(field, null, { shouldValidate: true });

  const toggleCheckbox = (field, value) => {
    const currentValues = watch(field) || [];
    let newValues;

    if (currentValues.includes(value)) {
      // unselect
      newValues = currentValues.filter(v => v !== value);
    } else {
      // select
      newValues = [...currentValues, value];
    }

    setValue(field, newValues, { shouldValidate: true });
  };
  const toggleSingleOption = (field, value) => {
    const currentValue = watch(field);

    if (currentValue === value) {
      // unselect if the same option is clicked again
      setValue(field, null, { shouldValidate: true });
    } else {
      setValue(field, value, { shouldValidate: true });
    }
  };


  const clearApgarScores = () => {
    setValue('apgar_1min', '', { shouldValidate: true });
    setValue('apgar_5min', '', { shouldValidate: true });
    setValue('apgar_10min', '', { shouldValidate: true });
    setValue('apgar_1min_na', false, { shouldValidate: true });
    setValue('apgar_5min_na', false, { shouldValidate: true });
    setValue('apgar_10min_na', false, { shouldValidate: true });
  };

  const toggleApgarNA = (field, naField) => {
    const currentNA = watch(naField);
    if (currentNA) {
      setValue(naField, false, { shouldValidate: true });
    } else {
      setValue(naField, true, { shouldValidate: true });
      setValue(field, '', { shouldValidate: true });
    }
  };

  const toggleResuscitationMethod = (field) => {
    const currentValue = watch(field);
    setValue(field, !currentValue, { shouldValidate: true });
  };

  const [showSuccessPopup, setShowSuccessPopup] = useState(false);

  const onSubmit = async (data) => {
    try {
      const payload = mapSection3Data(data, name, TNR);
      await dispatch(saveSectionThunk({
        num: "3",
        formData: payload.data
      })).unwrap();

      // Update section progress after successful save
      await dispatch(updateSectionProgressThunk({
        TNR,
        num: "3"
      })).unwrap();

      setShowSuccessPopup(true);
    } catch (error) {
      console.error('Error submitting form:', error);
      Alert.alert("Error", error.message || "Failed to save section 3 data");
    }
  };

  const onError = (errors) => {
    // console.log('Form validation errors:', errors);
  };

  const handleSuccessPopupClose = () => {
    setShowSuccessPopup(false);
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <ScreenWrapper topColor={colors.header?.background || colors.header?.bg || '#fff'}>
      <CommonHeader title={"Section 3"} onBackPress={handleBack} />
      <KeyboardAwareScrollView
        style={{ flex: 1 }}
        contentContainerStyle={styles.scrollContainer}
      >
        {/* Header */}
        <View style={styles.subHeadingContainer}>
          <Text style={styles.subHeader}>Section 3: Perinatal data</Text>
        </View>

        {/* Mode of delivery */}
        <View style={styles.inputContainer}>
          <View style={styles.labelWithClear}>
            <Text style={styles.label}>{t.t('Mode of delivery')}</Text>
            <TouchableOpacity onPress={() => clearField('mode_of_delivery')}>
              <Text style={styles.clearText}>{t.t('Clear')}</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('mode_of_delivery', 'vaginal')}>
            <View style={globalStyles.radioButton}>
              {watch('mode_of_delivery') === 'vaginal' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('Vaginal')}</Text>
          </TouchableOpacity>
        </View>

        {/* Sub-options for Vaginal */}
        {watch('mode_of_delivery') === 'vaginal' || watch('mode_of_delivery') === 'vaginal' ? (
          <View style={styles.subRadioGroup}>
            <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('vaginal_type', 'with_instrument')}>
              <View style={globalStyles.radioButton}>
                {watch('vaginal_type') === 'with_instrument' && <View style={globalStyles.radioButtonSelected} />}
              </View>
              <Text style={styles.radioLabel}>{t.t('With instrument')}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('vaginal_type', 'without_instrument')}>
              <View style={globalStyles.radioButton}>
                {watch('vaginal_type') === 'without_instrument' && <View style={globalStyles.radioButtonSelected} />}
              </View>
              <Text style={styles.radioLabel}>{t.t('Without instrument')}</Text>
            </TouchableOpacity>
            {errors.vaginal_type && <Text style={styles.errorText}>{errors.vaginal_type.message}</Text>}
          </View>
        ) : null}

        {/* Error message for vaginal type */}
        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('mode_of_delivery', 'caesarean_section')}>
            <View style={globalStyles.radioButton}>
              {watch('mode_of_delivery') === 'caesarean_section' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('Caesarean section')}</Text>
          </TouchableOpacity>
        </View>

        {/* Caesarean section sub-options */}
        {/* Caesarean section sub-options */}
        {watch('mode_of_delivery') === 'caesarean_section' && (
          <View style={styles.subRadioGroup}>
            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleSingleOption('caesarean_types', 'elective_previous')}
            >
              <View
                style={[
                  globalStyles.checkboxBox,
                  watch('caesarean_types') === 'elective_previous' && globalStyles.checkboxChecked,
                ]}
              >
                {watch('caesarean_types') === 'elective_previous' && (
                  <Text style={globalStyles.checkmark}>✓</Text>
                )}
              </View>
              <Text style={styles.checkboxLabel}>
                {t.t('Elective c-section / previous c-section')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleSingleOption('caesarean_types', 'emergency')}
            >
              <View
                style={[
                  globalStyles.checkboxBox,
                  watch('caesarean_types') === 'emergency' && globalStyles.checkboxChecked,
                ]}
              >
                {watch('caesarean_types') === 'emergency' && (
                  <Text style={globalStyles.checkmark}>✓</Text>
                )}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('Emergency c-section')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleSingleOption('caesarean_types', 'cephalopelvic_disproportion')}
            >
              <View
                style={[
                  globalStyles.checkboxBox,
                  watch('caesarean_types') === 'cephalopelvic_disproportion' &&
                  globalStyles.checkboxChecked,
                ]}
              >
                {watch('caesarean_types') === 'cephalopelvic_disproportion' && (
                  <Text style={globalStyles.checkmark}>✓</Text>
                )}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('Cephalopelvic disproportion')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleSingleOption('caesarean_types', 'other')}
            >
              <View
                style={[
                  globalStyles.checkboxBox,
                  watch('caesarean_types') === 'other' && globalStyles.checkboxChecked,
                ]}
              >
                {watch('caesarean_types') === 'other' && (
                  <Text style={globalStyles.checkmark}>✓</Text>
                )}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('Other')}</Text>
            </TouchableOpacity>

            {/* "Other" input field */}
            {watch('caesarean_types') === 'other' && (
              <View style={styles.otherInputContainer}>
                <Controller
                  control={control}
                  name="caesarean_other_specify"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={styles.input}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      value={value}
                      placeholder={t.t('Please specify')}
                    />
                  )}
                />
                {errors.caesarean_other_specify && (
                  <Text style={styles.errorText}>
                    {errors.caesarean_other_specify.message}
                  </Text>
                )}
              </View>
            )}

            {errors.caesarean_types && (
              <Text style={styles.errorText}>{errors.caesarean_types.message}</Text>
            )}
          </View>
        )}


        {errors.mode_of_delivery && <Text style={styles.errorText}>{errors.mode_of_delivery.message}</Text>}

        {/* Apgar score */}
        <View style={styles.inputContainer}>
          <View style={styles.labelWithClear}>
            <Text style={styles.label}>{t.t('Apgar score')}</Text>
            <TouchableOpacity onPress={clearApgarScores}>
              <Text style={styles.clearText}>{t.t('Clear')}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.sublabel}>{t.t('Select N/A')}</Text>

          {/* Time intervals */}
          <View style={styles.apgarContainer}>
            <View style={styles.apgarInputsRow}>
              {/* 1 min */}
              <View style={styles.apgarInputContainer}>
                <Text style={styles.apgarTimeLabel}>{t.t('1 min')}</Text>
                <Controller
                  control={control}
                  name="apgar_1min"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={[
                        styles.apgarInput,
                        watch('apgar_1min_na') && { backgroundColor: '#f0f0f0' }
                      ]}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      value={value}
                      placeholder={t.t('Score')}
                      keyboardType="numeric"
                      editable={!watch('apgar_1min_na')}
                    />
                  )}
                />
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => toggleApgarNA('apgar_1min', 'apgar_1min_na')}
                >
                  <View style={[globalStyles.checkboxBox, watch('apgar_1min_na') && globalStyles.checkboxChecked]}>
                    {watch('apgar_1min_na') && <Text style={globalStyles.checkmark}>✓</Text>}
                  </View>
                  <Text style={styles.checkboxLabel}>N/A</Text>
                </TouchableOpacity>
              </View>

              {/* 5 min */}
              <View style={styles.apgarInputContainer}>
                <Text style={styles.apgarTimeLabel}>{t.t('5 min')}</Text>
                <Controller
                  control={control}
                  name="apgar_5min"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={[
                        styles.apgarInput,
                        watch('apgar_5min_na') && { backgroundColor: '#f0f0f0' }
                      ]}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      value={value}
                      placeholder={t.t('Score')}
                      keyboardType="numeric"
                      editable={!watch('apgar_5min_na')}
                    />
                  )}
                />
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => toggleApgarNA('apgar_5min', 'apgar_5min_na')}
                >
                  <View style={[globalStyles.checkboxBox, watch('apgar_5min_na') && globalStyles.checkboxChecked]}>
                    {watch('apgar_5min_na') && <Text style={globalStyles.checkmark}>✓</Text>}
                  </View>
                  <Text style={styles.checkboxLabel}>N/A</Text>
                </TouchableOpacity>
              </View>

              {/* 10 min */}
              <View style={styles.apgarInputContainer}>
                <Text style={styles.apgarTimeLabel}>{t.t('10 min')}</Text>
                <Controller
                  control={control}
                  name="apgar_10min"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={[
                        styles.apgarInput,
                        watch('apgar_10min_na') && { backgroundColor: '#f0f0f0' }
                      ]}
                      onBlur={onBlur}
                      onChangeText={onChange}
                      value={value}
                      placeholder={t.t('Score')}
                      keyboardType="numeric"
                      editable={!watch('apgar_10min_na')}
                    />
                  )}
                />
                <TouchableOpacity
                  style={styles.checkboxContainer}
                  onPress={() => toggleApgarNA('apgar_10min', 'apgar_10min_na')}
                >
                  <View style={[globalStyles.checkboxBox, watch('apgar_10min_na') && globalStyles.checkboxChecked]}>
                    {watch('apgar_10min_na') && <Text style={globalStyles.checkmark}>✓</Text>}
                  </View>
                  <Text style={styles.checkboxLabel}>N/A</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {errors.apgar_1min && <Text style={styles.errorText}>{errors.apgar_1min.message}</Text>}
          {errors.apgar_5min && <Text style={styles.errorText}>{errors.apgar_5min.message}</Text>}
          {errors.apgar_10min && <Text style={styles.errorText}>{errors.apgar_10min.message}</Text>}
        </View>

        {/* Resuscitation */}
        <View style={styles.inputContainer}>
          <View style={styles.labelWithClear}>
            <Text style={styles.label}>{t.t('Resuscitation')}</Text>
            <TouchableOpacity onPress={() => clearField('resuscitation')}>
              <Text style={styles.clearText}>{t.t('Clear')}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.sublabel}>{t.t('Select N/A')}</Text>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('resuscitation', 'no')}>
            <View style={globalStyles.radioButton}>
              {watch('resuscitation') === 'no' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('No')}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('resuscitation', 'yes')}>
            <View style={globalStyles.radioButton}>
              {watch('resuscitation') === 'yes' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('Yes')}</Text>
          </TouchableOpacity>
        </View>
        {/* Resuscitation Methods - Show when Yes is selected */}
        {watch('resuscitation') === 'yes' && (
          <View style={styles.subRadioGroup}>
            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleResuscitationMethod('resuscitation_cpap')}
            >
              <View style={[globalStyles.checkboxBox, watch('resuscitation_cpap') && globalStyles.checkboxChecked]}>
                {watch('resuscitation_cpap') && <Text style={globalStyles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('CPAP')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleResuscitationMethod('resuscitation_ppv')}
            >
              <View style={[globalStyles.checkboxBox, watch('resuscitation_ppv') && globalStyles.checkboxChecked]}>
                {watch('resuscitation_ppv') && <Text style={globalStyles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('PPV')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleResuscitationMethod('resuscitation_intubation')}
            >
              <View style={[globalStyles.checkboxBox, watch('resuscitation_intubation') && globalStyles.checkboxChecked]}>
                {watch('resuscitation_intubation') && <Text style={globalStyles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('Intubation')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleResuscitationMethod('resuscitation_chest_compression')}
            >
              <View style={[globalStyles.checkboxBox, watch('resuscitation_chest_compression') && globalStyles.checkboxChecked]}>
                {watch('resuscitation_chest_compression') && <Text style={globalStyles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('Chest compression')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => toggleResuscitationMethod('resuscitation_epinephrine')}
            >
              <View style={[globalStyles.checkboxBox, watch('resuscitation_epinephrine') && globalStyles.checkboxChecked]}>
                {watch('resuscitation_epinephrine') && <Text style={globalStyles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxLabel}>{t.t('Epinephrine')}</Text>
            </TouchableOpacity>
          </View>
        )}
        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('resuscitation', 'na')}>
            <View style={globalStyles.radioButton}>
              {watch('resuscitation') === 'na' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>N/A</Text>
          </TouchableOpacity>
          {errors.resuscitation && <Text style={styles.errorText}>{errors.resuscitation.message}</Text>}
        </View>



        {/* Cord blood / 1st hour blood gas */}
        <View style={styles.inputContainer}>
          <View style={styles.labelWithClear}>
            <Text style={styles.label}>{t.t('Cord blood / 1st hour blood gas')}</Text>
            <TouchableOpacity onPress={() => clearField('cord_blood_gas')}>
              <Text style={styles.clearText}>{t.t('Clear')}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.sublabel}>{t.t('Select N/A')}</Text>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('cord_blood_gas', 'not_done')}>
            <View style={globalStyles.radioButton}>
              {watch('cord_blood_gas') === 'not_done' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('Not done')}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('cord_blood_gas', 'available')}>
            <View style={globalStyles.radioButton}>
              {watch('cord_blood_gas') === 'available' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('Available')}</Text>
          </TouchableOpacity>
        </View>
        {/* Blood gas values - Show when Available is selected */}
        {watch('cord_blood_gas') === 'available' && (
          <View style={styles.subRadioGroup}>
            {/* pH */}
            <View style={styles.bloodGasField}>
              <Text style={styles.bloodGasLabel}>{t.t('pH')}</Text>
              <Controller
                control={control}
                name="cord_blood_ph"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={styles.bloodGasInput}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    placeholder={t.t('Please enter pH')}
                    keyboardType="numeric"
                  />
                )}
              />
              {errors.cord_blood_ph && <Text style={styles.errorText}>{errors.cord_blood_ph.message}</Text>}
            </View>

            {/* pCO₂ */}
            <View style={styles.bloodGasField}>
              <Text style={styles.bloodGasLabel}>{t.t('pCO₂ (optional)')}</Text>
              <Controller
                control={control}
                name="cord_blood_pco2"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={styles.bloodGasInput}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    placeholder={t.t('Please enter pCO₂')}
                    keyboardType="numeric"
                  />
                )}
              />
            </View>

            {/* HCO₂ */}
            <View style={styles.bloodGasField}>
              <Text style={styles.bloodGasLabel}>{t.t('HCO₂ (optional)')}</Text>
              <Controller
                control={control}
                name="cord_blood_hco2"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={styles.bloodGasInput}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    placeholder={t.t('Please enter HCO₂')}
                    keyboardType="numeric"
                  />
                )}
              />
            </View>

            {/* BE */}
            <View style={styles.bloodGasField}>
              <Text style={styles.bloodGasLabel}>{t.t('BE (optional)')}</Text>
              <Controller
                control={control}
                name="cord_blood_be"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={styles.bloodGasInput}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    placeholder={t.t('Please enter BE')}
                    keyboardType="numeric"
                  />
                )}
              />
            </View>
          </View>
        )}
        <View style={styles.inputContainer}>
          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('cord_blood_gas', 'na')}>
            <View style={globalStyles.radioButton}>
              {watch('cord_blood_gas') === 'na' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>N/A</Text>
          </TouchableOpacity>
          {errors.cord_blood_gas && <Text style={styles.errorText}>{errors.cord_blood_gas.message}</Text>}
        </View>



        {/* Delayed cord clamping / Milking */}
        <View style={styles.inputContainer}>
          <View style={styles.labelWithClear}>
            <Text style={styles.label}>{t.t('Delayed cord clamping / Milking')}</Text>
            <TouchableOpacity onPress={() => clearField('delayed_cord_clamping')}>
              <Text style={styles.clearText}>{t.t('Clear')}</Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.sublabel}>{t.t('Select N/A')}</Text>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('delayed_cord_clamping', 'no')}>
            <View style={globalStyles.radioButton}>
              {watch('delayed_cord_clamping') === 'no' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('No')}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('delayed_cord_clamping', 'yes')}>
            <View style={globalStyles.radioButton}>
              {watch('delayed_cord_clamping') === 'yes' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>{t.t('Yes')}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.radioContainer} onPress={() => radioSelect('delayed_cord_clamping', 'na')}>
            <View style={globalStyles.radioButton}>
              {watch('delayed_cord_clamping') === 'na' && <View style={globalStyles.radioButtonSelected} />}
            </View>
            <Text style={styles.radioLabel}>N/A</Text>
          </TouchableOpacity>

          {errors.delayed_cord_clamping && <Text style={styles.errorText}>{errors.delayed_cord_clamping.message}</Text>}
        </View>

      </KeyboardAwareScrollView>
      <CommonFooter
        onSubmit={handleSubmit(onSubmit, onError)}
        buttonText={t.t('Save')}
        loading={loading}
      />

      <PopupCard
        visible={showSuccessPopup}
        title={t.t('Success')}
        description={t.t('Section 3 data saved successfully!')}
        backgroundColor={'#7FC5C6'}
        svg={<CompleteIcon width={120} height={120} />}
        buttons={[
          {
            label: 'OK',
            onPress: handleSuccessPopupClose,
            color: colors.text?.cardText || '#fff'
          }
        ]}
        onDismiss={handleSuccessPopupClose}
      />
    </ScreenWrapper>
  );
};

export default Section3Form;

const styles = StyleSheet.create({
  scrollContainer: {
    // padding: 24,
  },
  subHeadingContainer: {
    marginBottom: 20,
    paddingLeft: 16,
    marginTop: 8
  },
  subHeader: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border?.gray || '#D1D5DB',
    fontSize: 18,
    fontWeight: 'bold',
    paddingBottom: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 14,
    color: colors.text?.muted || '#6b7280',
    marginBottom: 16,
  },
  inputContainer: {
    paddingLeft: 16,
    paddingRight: 16,
  },
  labelWithClear: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  clearText: {
    fontSize: 14,
    color: colors.button?.primary || '#1E88E5',
    fontWeight: '600',
  },
  sublabel: {
    fontSize: 12,
    color: colors.text?.muted || '#6b7280',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border?.gray || '#D1D5DB',
    padding: 10,
    borderRadius: 6,
    backgroundColor: colors.background?.white || '#fff',
  },
  radioRow: {
    flexDirection: 'column',
    gap: 8,
  },
  radioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  radioLabel: {
    fontSize: 14,
  },
  subRadioGroup: {
    marginBottom: 10,
    backgroundColor: "#F5F5F5",
    paddingVertical: 6,
    paddingHorizontal: 25
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkboxLabel: {
    marginLeft: 8,
    fontSize: 14,
  },
  resuscitationMethodsContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  bloodGasContainer: {
    marginTop: 12,
    marginLeft: 16,
    backgroundColor: '#F8F9FA',
    padding: 12,
    borderRadius: 8,
  },
  bloodGasField: {
    marginBottom: 12,
  },
  bloodGasLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
    color: '#333',
  },
  bloodGasInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  apgarContainer: {
    marginTop: 8
  },
  apgarTimeLabels: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  apgarTimeLabel: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  apgarInputsRow: {
    flexDirection: 'row',
  },
  apgarInputContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  apgarInput: {
    borderWidth: 1,
    borderColor: colors.border?.gray || '#D1D5DB',
    padding: 10,
    borderRadius: 6,
    backgroundColor: colors.background?.white || '#fff',
    textAlign: 'center',
    width: 80,
    marginBottom: 8,
  },
  errorText: {
    color: colors.text?.error || '#DC2626',
    marginTop: 4,
  },
  footerButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    marginTop: 12,
  },
  navButton: {
    backgroundColor: colors.button?.primary || '#1E88E5',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  navButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  secondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.button?.primary || '#1E88E5',
  },
  secondaryText: {
    color: colors.button?.primary || '#1E88E5',
  },
  footerSave: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
  },
  button: {
    backgroundColor: colors.button?.primary || '#1E88E5',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  otherInputContainer: {
    marginTop: 8,
    marginBottom: 8,
    width: '100%'
  }
});
