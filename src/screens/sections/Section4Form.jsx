import { useContext, useState } from 'react';
import { Text, View, TouchableOpacity, StyleSheet, TextInput, Alert, Platform } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useDispatch, useSelector } from 'react-redux';

import ScreenWrapper from '../../components/ScreenWrapper';
import CommonHeader from '../../components/CommonHeader';
import CommonFooter from '../../components/CommonFooter';
import CommonDropdown from '../../components/CommonDropdown';
import LanguageContext from '../../context/LanguageContext';
import { section4Schema, section4DefaultValues } from '../../schemas/section4Schema';
import mapSection4Data from '../../helpers/sections/section4';
import colors from '../../theme/colors';
import globalStyles from '../../styles/globalStyles';
import { getMonthOptions, getYearOptionsRange, getDayOptions } from '../../utils/dateUtils';
import PopupCard from '../../components/PopupCard';
import CompleteIcon from '../../assets/img/complete.svg';
import { saveSectionThunk, updateSectionProgressThunk } from '../../features/sections/sectionSlice';

const Section4Form = () => {
    const router = useRouter();
    const { t } = useContext(LanguageContext);
    const dispatch = useDispatch();
    const { loading } = useSelector((state) => state.sections);
    const { user } = useSelector((state) => state.auth);
    const { TNR, hospital } = useLocalSearchParams();

    // Birth date dropdown state
    const [birthDay, setBirthDay] = useState('');
    const [birthMonth, setBirthMonth] = useState('');
    const [birthYear, setBirthYear] = useState('');
    const [showSuccessPopup, setShowSuccessPopup] = useState(false);

    const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm({
        resolver: zodResolver(section4Schema),
        defaultValues: section4DefaultValues,
    });

    const onSubmit = async (data) => {
        try {
            const payload = mapSection4Data(data, user.username, TNR, hospital);
            await dispatch(saveSectionThunk({
                num: "4",
                formData: payload.data
            })).unwrap();

            // Update section progress after successful save
            await dispatch(updateSectionProgressThunk({
                TNR,
                num: "4"
            })).unwrap();

            setShowSuccessPopup(true);
        } catch (error) {
            console.error('Error submitting form:', error);
            Alert.alert("Error", error.message || "Failed to save section 4 data");
        }
    };

    const onError = (errors) => {
        // console.log('Form validation errors:', errors);
    };

    const handleBack = () => {
        router.back();
    };

    const handleSuccessPopupClose = () => {
        setShowSuccessPopup(false);
    };

    const toggleNA = (field) => {
        const currentValue = watch(`${field}_na`);
        setValue(`${field}_na`, !currentValue);
        if (!currentValue) {
            // If setting to N/A, clear the input field
            setValue(field, '');
        }
    };

    return (
        <ScreenWrapper topColor={colors.header.background}>
            <CommonHeader title="Section 4" onBackPress={handleBack} />
            <KeyboardAwareScrollView
                style={{ flex: 1, backgroundColor: colors.white }}
                contentContainerStyle={styles.scrollContainer}
                extraScrollHeight={Platform.OS === 'android' ? 120 : 0}
                enableOnAndroid={true}
                enableResetScrollToCoords={Platform.OS === 'ios' ? false : true}
            >
                <View style={styles.subHeadingContainer}>
                    <Text style={styles.subHeader}>Section 4: Neonatal Data</Text>
                </View>

                {/* Date of birth */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Date of birth')}</Text>
                        <TouchableOpacity onPress={() => {
                            setBirthDay('');
                            setBirthMonth('');
                            setBirthYear('');
                            setValue('birth_date', '');
                        }}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={{ flexDirection: 'row', gap: 10 }}>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Day'), value: 'header', header: true }, ...getDayOptions(birthYear, birthMonth)]}
                                value={birthDay}
                                placeholder={t.t('Day')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Day')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setBirthDay(null);
                                        setValue('birth_date', '');
                                        return;
                                    }
                                    setBirthDay(item.value);
                                    const yy = (birthYear || '').toString().padStart(4, '');
                                    const mm = (birthMonth || '').toString().padStart(2, '');
                                    const dd = (item.value || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('birth_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    }
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Month'), value: 'header', header: true }, ...getMonthOptions()]}
                                value={birthMonth}
                                placeholder={t.t('Month')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Month')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setBirthMonth(null);
                                        setBirthDay(null);
                                        setValue('birth_date', '');
                                        return;
                                    }
                                    const newMonth = item.value;
                                    setBirthMonth(newMonth);

                                    // Reset day if it's invalid for the new month
                                    const maxDaysLen = getDayOptions(birthYear, newMonth).length;
                                    if (birthDay && parseInt(birthDay, 10) > maxDaysLen) {
                                        setBirthDay(null);
                                    }

                                    // Update the date if we have all components
                                    const yy = (birthYear || '').toString().padStart(4, '');
                                    const mm = (newMonth || '').toString().padStart(2, '');
                                    const dd = (birthDay || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('birth_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    } else {
                                        setValue('birth_date', '');
                                    }
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Year'), value: 'header', header: true }, ...getYearOptionsRange(4, 10)]}
                                value={birthYear}
                                placeholder={t.t('Year')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Year')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setBirthYear(null);
                                        setBirthDay(null);  // Reset day since it might be invalid without a year
                                        setValue('birth_date', '');
                                        return;
                                    }

                                    const newYear = item.value;
                                    setBirthYear(newYear);

                                    // Reset day if it's invalid for the new year/month
                                    const maxDaysLen = getDayOptions(newYear, birthMonth).length;
                                    if (birthDay && parseInt(birthDay, 10) > maxDaysLen) {
                                        setBirthDay(null);
                                    }

                                    // Update the date if we have all components
                                    const yy = (newYear || '').toString().padStart(4, '');
                                    const mm = (birthMonth || '').toString().padStart(2, '');
                                    const dd = (birthDay || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('birth_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    } else {
                                        setValue('birth_date', '');
                                    }
                                }}
                            />
                        </View>
                    </View>
                    {errors.birth_date && <Text style={styles.errorText}>{errors.birth_date.message}</Text>}
                    {errors.birth_day && <Text style={styles.errorText}>{errors.birth_day.message}</Text>}
                    {errors.birth_month && <Text style={styles.errorText}>{errors.birth_month.message}</Text>}
                    {errors.birth_year && <Text style={styles.errorText}>{errors.birth_year.message}</Text>}
                </View>

                {/* Time of birth */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Time of birth')}</Text>
                        <TouchableOpacity onPress={() => {
                            setValue('birth_hour', '');
                            setValue('birth_minute', '');
                        }}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.timeRow}>
                        <Controller
                            control={control}
                            name="birth_hour"
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    style={[styles.input]}
                                    placeholder={'Hours'}
                                    onBlur={onBlur}
                                    onChangeText={onChange}
                                    value={value}
                                    keyboardType="numeric"
                                    maxLength={2}
                                />
                            )}
                        />

                        <Controller
                            control={control}
                            name="birth_minute"
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    style={styles.input}
                                    placeholder={'Minute'}
                                    onBlur={onBlur}
                                    onChangeText={onChange}
                                    value={value}
                                    keyboardType="numeric"
                                    maxLength={2}
                                />
                            )}
                        />
                    </View>
                    {errors.birth_hour && <Text style={styles.errorText}>{errors.birth_hour.message}</Text>}
                    {errors.birth_minute && <Text style={styles.errorText}>{errors.birth_minute.message}</Text>}
                </View>

                {/* Gender */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Gender')}</Text>
                        <TouchableOpacity onPress={() => setValue('gender', null)}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <Controller
                        control={control}
                        name="gender"
                        render={({ field: { onChange, value } }) => (
                            <View>
                                <TouchableOpacity
                                    style={styles.radioContainer}
                                    onPress={() => onChange('male')}
                                >
                                    <View style={globalStyles.radioButton}>
                                        {value === 'male' && <View style={globalStyles.radioButtonSelected} />}
                                    </View>
                                    <Text style={styles.radioLabel}>{t.t('Male')}</Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.radioContainer}
                                    onPress={() => onChange('female')}
                                >
                                    <View style={globalStyles.radioButton}>
                                        {value === 'female' && <View style={globalStyles.radioButtonSelected} />}
                                    </View>
                                    <Text style={styles.radioLabel}>{t.t('Female')}</Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.radioContainer}
                                    onPress={() => onChange('ambiguous')}
                                >
                                    <View style={globalStyles.radioButton}>
                                        {value === 'ambiguous' && <View style={globalStyles.radioButtonSelected} />}
                                    </View>
                                    <Text style={styles.radioLabel}>{t.t('Ambiguous')}</Text>
                                </TouchableOpacity>
                            </View>
                        )}
                    />

                    {errors.gender && <Text style={styles.errorText}>{errors.gender.message}</Text>}
                </View>

                {/* Gestational age */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Gestational age (weeks:days)')}</Text>
                        <TouchableOpacity onPress={() => {
                            setValue('gestational_weeks', '');
                            setValue('gestational_days', '');
                        }}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.gestationalRow}>
                        <Controller
                            control={control}
                            name="gestational_weeks"
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    style={[styles.input]}
                                    placeholder={'weeks'}
                                    onBlur={onBlur}
                                    onChangeText={onChange}
                                    value={value}
                                    keyboardType="numeric"
                                />
                            )}
                        />

                        <Controller
                            control={control}
                            name="gestational_days"
                            render={({ field: { onChange, onBlur, value } }) => (
                                <TextInput
                                    style={[styles.input]}
                                    placeholder={'days'}
                                    onBlur={onBlur}
                                    onChangeText={onChange}
                                    value={value}
                                    keyboardType="numeric"
                                />
                            )}
                        />
                    </View>
                    {errors.gestational_weeks && <Text style={styles.errorText}>{errors.gestational_weeks.message}</Text>}
                    {errors.gestational_days && <Text style={styles.errorText}>{errors.gestational_days.message}</Text>}
                </View>

                {/* Birth weight */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Birth weight (g)')}</Text>
                        <TouchableOpacity onPress={() => setValue('birth_weight', '')}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <Controller
                        control={control}
                        name="birth_weight"
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                style={[styles.input]}
                                placeholder={t.t('Birth weight')}
                                onBlur={onBlur}
                                onChangeText={onChange}
                                value={value}
                                keyboardType="numeric"
                            />
                        )}
                    />

                    {errors.birth_weight && <Text style={styles.errorText}>{errors.birth_weight.message}</Text>}
                </View>

                {/* Length */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Length (cm)')}</Text>
                        <TouchableOpacity onPress={() => setValue('length', '')}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <Controller
                        control={control}
                        name="length"
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                style={[
                                    styles.input,
                                    watch('length_na') && styles.disabledInput
                                ]}
                                placeholder={t.t('Length')}
                                onBlur={onBlur}
                                onChangeText={onChange}
                                value={value}
                                keyboardType="numeric"
                                editable={!watch('length_na')}
                            />
                        )}
                    />

                    <TouchableOpacity
                        style={styles.checkboxContainer}
                        onPress={() => toggleNA('length')}
                    >
                        <View style={[globalStyles.checkboxBox, watch('length_na') && globalStyles.checkboxChecked]}>
                            {watch('length_na') && <Text style={globalStyles.checkmark}>✓</Text>}
                        </View>
                        <Text style={styles.checkboxLabel}>{'N/A'}</Text>
                    </TouchableOpacity>

                    {errors.length && <Text style={styles.errorText}>{errors.length.message}</Text>}
                </View>

                {/* Head circumference */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Head circumference (cm)')}</Text>
                        <TouchableOpacity onPress={() => setValue('head_circumference', '')}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <Controller
                        control={control}
                        name="head_circumference"
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                style={[
                                    styles.input,
                                    watch('head_circumference_na') && styles.disabledInput
                                ]}
                                placeholder={t.t('Head circumference')}
                                onBlur={onBlur}
                                onChangeText={onChange}
                                value={value}
                                keyboardType="numeric"
                                editable={!watch('head_circumference_na')}
                            />
                        )}
                    />

                    <TouchableOpacity
                        style={styles.checkboxContainer}
                        onPress={() => toggleNA('head_circumference')}
                    >
                        <View style={[globalStyles.checkboxBox, watch('head_circumference_na') && globalStyles.checkboxChecked]}>
                            {watch('head_circumference_na') && <Text style={globalStyles.checkmark}>✓</Text>}
                        </View>
                        <Text style={styles.checkboxLabel}>{"N/A"}</Text>
                    </TouchableOpacity>

                    {errors.head_circumference && <Text style={styles.errorText}>{errors.head_circumference.message}</Text>}
                </View>

                {/* Growth status */}
                <View style={styles.inputContainer}>
                    <View style={styles.labelWithClear}>
                        <Text style={styles.label}>{t.t('Growth status (Fenton 2013)')}</Text>
                        <TouchableOpacity onPress={() => setValue('growth_status', null)}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>

                    <Controller
                        control={control}
                        name="growth_status"
                        render={({ field: { onChange, value } }) => (
                            <View>
                                <TouchableOpacity
                                    style={[styles.radioContainer]}
                                    onPress={() => onChange('sga')}
                                >
                                    <View style={globalStyles.radioButton}>
                                        {value === 'sga' && <View style={globalStyles.radioButtonSelected} />}
                                    </View>
                                    <Text style={styles.radioLabel}>
                                        {t.t('SGA (< 10')}
                                        <Text style={styles.superscript}>th</Text>
                                        {' '}
                                        {t.t('percentile)')}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.radioContainer}
                                    onPress={() => onChange('aga')}
                                >
                                    <View style={globalStyles.radioButton}>
                                        {value === 'aga' && <View style={globalStyles.radioButtonSelected} />}
                                    </View>
                                    <Text style={[styles.radioLabel]}>
                                        {t.t('AGA (10-90')}
                                        <Text style={styles.superscript}>th</Text>
                                        {' '}
                                        {t.t('percentile)')}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.radioContainer}
                                    onPress={() => onChange('lga')}
                                >
                                    <View style={globalStyles.radioButton}>
                                        {value === 'lga' && <View style={globalStyles.radioButtonSelected} />}
                                    </View>
                                    <Text style={styles.radioLabel}>
                                        {t.t('LGA (> 90')}
                                        <Text style={styles.superscript}>th</Text>
                                        {' '}
                                        {t.t('percentile)')}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        )}
                    />

                    {errors.growth_status && <Text style={styles.errorText}>{errors.growth_status.message}</Text>}
                </View>

            </KeyboardAwareScrollView>
            <CommonFooter
                onSubmit={handleSubmit(onSubmit, onError)}
                buttonText={t.t('Save')}
                loading={loading}
            />

            <PopupCard
                visible={showSuccessPopup}
                title={t.t('Success')}
                description={t.t('Section 4 data saved successfully!')}
                backgroundColor={'#7FC5C6'}
                svg={<CompleteIcon width={120} height={120} />}
                buttons={[
                    {
                        label: 'OK',
                        onPress: handleSuccessPopupClose,
                        color: colors.text?.cardText || '#fff'
                    }
                ]}
                onDismiss={handleSuccessPopupClose}
            />
        </ScreenWrapper>
    );
};

const styles = StyleSheet.create({
    scrollContainer: {
        flexGrow: 1,
        paddingHorizontal: 20,
        paddingBottom: 20,
    },
    subHeadingContainer: {
        marginVertical: 20,
    },
    subHeader: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.text.primary,
    },
    inputContainer: {
        marginBottom: 20,
    },
    labelWithClear: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    label: {
        fontSize: 16,
        fontWeight: '600',
        color: colors.text.primary,
        flex: 1,
    },
    clearText: {
        fontSize: 14,
        color: colors.button?.primary || '#1E88E5',
        fontWeight: '600',
    },
    input: {
        flex: 1,
        borderWidth: 1,
        borderColor: colors.border?.gray || '#D1D5DB',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
        backgroundColor: colors.background?.white || '#fff',
        // textAlign: 'center',
    },
    disabledInput: {
        backgroundColor: colors.background.light,
        color: colors.text.disabled,
    },
    errorInput: {
        borderColor: '#ff4d4f',
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderWidth: 2,
        borderColor: colors.border,
        borderRadius: 4,
        marginRight: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkboxSelected: {
        backgroundColor: colors.primary,
        borderColor: colors.primary,
    },
    checkmark: {
        color: colors.white,
        fontSize: 12,
        fontWeight: 'bold',
    },
    checkboxLabel: {
        fontSize: 16,
        color: colors.text.primary,
    },
    radioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
    },
    radioLabel: {
        fontSize: 16,
        color: colors.text.primary,
        padding: 3
    },
    superscript: {
        fontSize: 12,
    },
    errorText: {
        color: '#ff4d4f',
        fontSize: 14,
        marginTop: 4,
    },
    dateRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 10,
    },
    timeRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 10,
    },
    gestationalRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 10,
    },
});

export default Section4Form;