import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { responsive } from '../../utils/responsive';
import ProfileSvg from '../../assets/img/profile.svg';
import colors from '../../theme/colors';

const ProfileHeader = ({ name, hospital }) => (
    <View style={styles.headerContainer}>
        <ProfileSvg width={128} height={128} style={styles.profileSvg} />
        <Text style={styles.name}>{name}</Text>
        <Text style={styles.hospital}>{hospital}</Text>
    </View>
);

const styles = StyleSheet.create({
    headerContainer: {
        height: 300,
        paddingBottom: 20,
        alignItems: 'center',
        justifyContent: 'flex-end',
        position: 'relative',
    },
    profileSvg: {
        marginBottom: 12,
    },
    name: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 8,
        color: colors.white,
    },
    hospital: {
        fontSize: 14,
        color: colors.placeholder,
        marginTop: 2,
        marginBottom: 8,
        textAlign: 'center',
        flexWrap: 'wrap',
    }
});

export default ProfileHeader; 