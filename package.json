{"name": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-community/datetimepicker": "8.4.4", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "expo": "^54.0.2", "expo-constants": "~18.0.8", "expo-dev-client": "~6.0.12", "expo-font": "~14.0.8", "expo-linking": "~8.0.8", "expo-localization": "~17.0.7", "expo-router": "~6.0.1", "expo-status-bar": "~3.0.8", "i18n-js": "^4.5.1", "react": "19.1.0", "react-hook-form": "^7.61.0", "react-native": "0.81.4", "react-native-dotenv": "^3.4.11", "react-native-element-dropdown": "^2.12.4", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-popover-view": "^6.1.0", "react-native-progress": "^5.0.1", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "zod": "^4.0.8"}, "devDependencies": {"@babel/core": "^7.20.0", "react-native-svg-transformer": "^1.5.1"}, "private": true}